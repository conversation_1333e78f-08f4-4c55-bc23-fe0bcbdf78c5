//+------------------------------------------------------------------+
//| TrackingGroupTest.mq4                                            |
//| 測試 TrackingGroup 類別的實現                                    |
//+------------------------------------------------------------------+
#property copyright "MQL4 Library"
#property version   "1.00"
#property strict

#include "StatisticsGroup copy.mqh"

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    Print("開始測試 TrackingGroup 類別...");
    
    // 測試基本功能
    TestBasicFunctionality();
    
    // 測試統計計算
    TestStatisticsCalculation();
    
    // 測試錯誤處理
    TestErrorHandling();
    
    Print("TrackingGroup 測試完成");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    Print("TrackingGroup 測試結束");
}

//+------------------------------------------------------------------+
//| 測試基本功能                                                     |
//+------------------------------------------------------------------+
void TestBasicFunctionality()
{
    Print("=== 測試基本功能 ===");
    
    // 創建 TrackingGroup 實例
    TrackingGroup* group = new TrackingGroup(Symbol(), 0);
    
    if (group == NULL)
    {
        Print("錯誤：無法創建 TrackingGroup 實例");
        return;
    }
    
    // 測試初始狀態
    Print("初始訂單數量：", group.GetOrderCount());
    
    // 測試統計獲取
    OrderGroupStats stats = group.GetStatistics();
    Print("初始統計 - 總訂單數：", stats.totalOrders, "，總盈虧：", stats.totalProfit);
    
    // 測試刷新功能
    group.Refresh();
    Print("刷新後訂單數量：", group.GetOrderCount());
    
    // 測試清空功能
    group.ClearAll();
    Print("清空後訂單數量：", group.GetOrderCount());
    
    delete group;
    Print("基本功能測試完成");
}

//+------------------------------------------------------------------+
//| 測試統計計算                                                     |
//+------------------------------------------------------------------+
void TestStatisticsCalculation()
{
    Print("=== 測試統計計算 ===");
    
    // 創建帶魔術數字的 TrackingGroup
    TrackingGroup* group = new TrackingGroup(Symbol(), 12345);
    
    if (group == NULL)
    {
        Print("錯誤：無法創建帶魔術數字的 TrackingGroup 實例");
        return;
    }
    
    // 執行追蹤
    group.Refresh();
    
    // 獲取詳細統計
    OrderGroupStats stats = group.GetStatistics();
    
    Print("統計結果：");
    Print("- 總訂單數：", stats.totalOrders);
    Print("- 總盈虧：", DoubleToString(stats.totalProfit, 2));
    Print("- 總手數：", DoubleToString(stats.totalLots, 2));
    Print("- 平均開倉價：", DoubleToString(stats.avgOpenPrice, 5));
    Print("- 最大盈利：", DoubleToString(stats.maxProfit, 2));
    Print("- 最大虧損：", DoubleToString(stats.maxLoss, 2));
    Print("- 平均持倉時間：", stats.avgHoldingTime, " 秒");
    
    if (stats.totalOrders > 0)
    {
        Print("- 最早訂單時間：", TimeToString(stats.oldestOrderTime));
        Print("- 最新訂單時間：", TimeToString(stats.newestOrderTime));
        Print("- 最早訂單價格：", DoubleToString(stats.oldestOrderPrice, 5));
        Print("- 最新訂單價格：", DoubleToString(stats.newestOrderPrice, 5));
    }
    
    delete group;
    Print("統計計算測試完成");
}

//+------------------------------------------------------------------+
//| 測試錯誤處理                                                     |
//+------------------------------------------------------------------+
void TestErrorHandling()
{
    Print("=== 測試錯誤處理 ===");
    
    TrackingGroup* group = new TrackingGroup("", 0);
    
    // 測試無效票號
    group.AddOrder(-1);
    group.AddOrder(0);
    
    // 測試不存在的票號
    group.AddOrder(999999);
    
    // 測試空群組操作
    group.ClearAll();
    group.GetStatistics();
    
    delete group;
    Print("錯誤處理測試完成");
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // 不需要在每個 tick 執行任何操作
}
