//+------------------------------------------------------------------+
//| OrderGroupCodec.mqh                                              |
//| 訂單群組編碼/解碼工具類別                                        |
//| 提供無狀態的群組編碼機制，支援群組索引管理                      |
//+------------------------------------------------------------------+
#property copyright "EA_Wizard Framework"
#property version   "1.00"
#property strict

struct EncodedData{
    bool    isSuccess;
    string  groupId;
};

struct DecodedData{
    bool    isSuccess;
    int     originalMagic;
    int     groupIndex;
};

//+------------------------------------------------------------------+
//| 訂單群組編解碼器類別                                             |
//| 純靜態工具類別，提供群組編碼/解碼功能                           |
//+------------------------------------------------------------------+
class OrderGroupCodec
{
private:
    // 編碼常數
    static const int                MAX_GROUP_INDEX;         // 最大群組索引 (兩位數)
    static const int                MIN_GROUP_INDEX;         // 最小群組索引
    static const int                MIN_MAGIC_RANGE;         // 魔術數字最小範圍
    static const int                MAX_MAGIC_RANGE;         // 魔術數字最大範圍

    //--- 內部編碼方法（私有化）
    static int                   EncodeMagicNumber(int originalMagic, int groupIndex);

    //--- 內部解碼方法（私有化）
    static string                EncodeGroupId(int originalMagic, int groupIndex);
    static int                   DecodeOriginalMagic(string groupId);
    static int                   DecodeGroupIndex(string groupId);
public:
    //--- 核心編解碼 API（外部使用）
    static EncodedData           Encode(int originalMagic, int groupIndex);
    static DecodedData           Decode(string groupId);

    //--- 驗證方法（外部使用）
    static bool                  IsValidMagicNumber(int magicNumber);
    static bool                  IsValidGroupIndex(int groupIndex);
    static bool                  IsValidGroupId(string groupId);

    //--- 常數獲取方法
    static int                   GetMinGroupIndex() { return MIN_GROUP_INDEX; }
    static int                   GetMaxGroupIndex() { return MAX_GROUP_INDEX; }
    static int                   GetMaxMagicRange() { return MAX_MAGIC_RANGE; }
    static int                   GetMinMagicRange() { return MIN_MAGIC_RANGE; }
};

// 靜態常數初始化
const int OrderGroupCodec::MAX_GROUP_INDEX = 99;
const int OrderGroupCodec::MIN_GROUP_INDEX = 1;
const int OrderGroupCodec::MIN_MAGIC_RANGE = 1000;
const int OrderGroupCodec::MAX_MAGIC_RANGE = 9999;

//+------------------------------------------------------------------+
//| 靜態編碼方法實作                                                 |
//+------------------------------------------------------------------+

// 內部編碼魔術數字與群組索引
static int OrderGroupCodec::EncodeMagicNumber(int originalMagic, int groupIndex)
{
    // 編碼邏輯：originalMagic * 100 + groupIndex
    // 例如：magic 1999, groupIndex 79 → 1999 * 100 + 79 = 199979
    return originalMagic * 100 + groupIndex;
}

// 主要編碼方法：生成群組ID字串
static string OrderGroupCodec::EncodeGroupId(int originalMagic, int groupIndex)
{
    int encodedMagic = EncodeMagicNumber(originalMagic, groupIndex);
    return StringFormat("%d", encodedMagic);
}

// 解碼原始魔術數字
static int OrderGroupCodec::DecodeOriginalMagic(string groupId)
{
    int encodedMagic = (int)StringToInteger(groupId);

    // 解碼邏輯：原始魔術數字 = encodedMagic / 100
    // 例如：299967 / 100 = 2999 (整數除法)
    return encodedMagic / 100;
}

// 解碼群組索引
static int OrderGroupCodec::DecodeGroupIndex(string groupId)
{
    int encodedMagic = (int)StringToInteger(groupId);

    // 群組索引提取邏輯：groupIndex = encodedMagic % 100
    // 例如：299967 % 100 = 67
    return encodedMagic % 100;
}

//+------------------------------------------------------------------+
//| 驗證方法實作                                                     |
//+------------------------------------------------------------------+

// 驗證群組索引是否有效
static bool OrderGroupCodec::IsValidGroupIndex(int groupIndex)
{
    return (groupIndex >= MIN_GROUP_INDEX && groupIndex <= MAX_GROUP_INDEX);
}

// 驗證魔術數字是否有效
static bool OrderGroupCodec::IsValidMagicNumber(int magicNumber)
{
    return (magicNumber >= MIN_MAGIC_RANGE && magicNumber <= MAX_MAGIC_RANGE);
}

// 驗證群組編碼格式是否正確
static bool OrderGroupCodec::IsValidGroupId(string groupId)
{
    if(StringLen(groupId) == 0)
        return false;

    // 檢查是否為純數字
    for(int i = 0; i < StringLen(groupId); i++)
    {
        ushort ch = StringGetCharacter(groupId, i);
        if(ch < '0' || ch > '9')
            return false;
    }

    // 檢查數值範圍
    int value = (int)StringToInteger(groupId);
    return (value > 0);
}

//+------------------------------------------------------------------+
//| 核心編解碼 API 實作                                               |
//+------------------------------------------------------------------+

// 編碼：將原始魔術數字和群組索引轉換為群組ID
static EncodedData OrderGroupCodec::Encode(int originalMagic, int groupIndex)
{
    EncodedData result;
    result.isSuccess = false;

    if(!IsValidMagicNumber(originalMagic) || !IsValidGroupIndex(groupIndex))
        return result;

    result.groupId = EncodeGroupId(originalMagic, groupIndex);
    result.isSuccess = true;
    return result;
}

// 解碼：將群組ID轉換回原始魔術數字和群組索引
static DecodedData OrderGroupCodec::Decode(string groupId)
{
    DecodedData result;
    result.isSuccess = false;

    if(!IsValidGroupId(groupId))
        return result;

    result.originalMagic = DecodeOriginalMagic(groupId);
    result.groupIndex = DecodeGroupIndex(groupId);
    result.isSuccess = true;
    return result;
}
