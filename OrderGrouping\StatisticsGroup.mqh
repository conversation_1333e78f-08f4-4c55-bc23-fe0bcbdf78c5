//+------------------------------------------------------------------+
//| StatisticsGroup.mqh                                                 |
//| Enhanced OrderGroup class with statistics and pool integration  |
//| Lightweight version without framework dependencies              |
//+------------------------------------------------------------------+
#property copyright "MQL4 Library"
#property version   "1.00"
#property strict

#include "../../mql4_module/mql4-lib/Trade/OrderGroup.mqh"
#include "../mql4-lib/Trade/OrderPool.mqh"
#include "MatcherBuilder.mqh"

//+------------------------------------------------------------------+
//| Order group statistics structure                                |
//+------------------------------------------------------------------+
struct OrderGroupStats
{
    int               totalOrders;        // 總訂單數
    double            totalProfit;        // 總盈虧
    double            totalLots;          // 總手數
    double            avgOpenPrice;       // 平均開倉價
    double            maxProfit;          // 最大盈利
    double            maxLoss;            // 最大虧損
    double            oldestOrderPrice;   // 最早訂單開倉價
    double            newestOrderPrice;   // 最新訂單開倉價
    datetime          oldestOrderTime;    // 最早訂單時間
    datetime          newestOrderTime;    // 最新訂單時間
    int               avgHoldingTime;     // 平均持倉時間（秒）
};

//+------------------------------------------------------------------+
//| Enhanced OrderGroup class with statistics and pool integration  |
//+------------------------------------------------------------------+
class StatisticsGroup : public OrderGroup
{
private:
    // Trading pool integration
    string            m_symbol;          // 交易符號
    int               m_magicNumber;     // 魔術數字


    TradingPool*      m_pool;            // 交易池

    // Statistics
    OrderGroupStats   m_stats;           // 群組統計

    //+------------------------------------------------------------------+
    //| 私有內部方法 - 純實現細節                                        |
    //+------------------------------------------------------------------+

    void              InitializePool();       // 初始化 TradingPool
    void              UpdateStatistics();     // 重新計算統計數據
    void              ResetStatistics();      // 重置統計數據

    //+------------------------------------------------------------------+
    //| 統計計算分解方法 - 利用 groupDoubleProperty                      |
    //+------------------------------------------------------------------+
    void              CalculateBasicStatistics();     // 基本統計（利用基類方法）
    void              CalculateProfitStatistics();    // 盈虧統計
    void              CalculateTimeStatistics();      // 時間統計
    void              CalculateAverageValues();       // 平均值計算

protected:
    //+------------------------------------------------------------------+
    //| 受保護方法 - 內部流程，子類可擴展                                |
    //+------------------------------------------------------------------+

    /// 執行訂單追蹤 - 子類可覆寫以自定義追蹤行為
    virtual void      ExecuteTracking();



public:
    //+------------------------------------------------------------------+
    //| 公共接口 - 外部可直接使用                                        |
    //+------------------------------------------------------------------+

    // 建構函數和解構函數
                      StatisticsGroup(string symbol = "", int magicNumber = 0);
    virtual          ~StatisticsGroup();

    /// 獲取群組統計信息 - 主要查詢接口
    OrderGroupStats   GetStatistics() { UpdateStatistics(); return m_stats; }

    /// 獲取群組中的訂單數量 - 基本查詢接口
    int               GetOrderCount() { return size(); }

    /// 刷新群組狀態 - 主要操作接口，執行完整的更新流程
    void              RefreshGroup() { clear(); ExecuteTracking(); UpdateStatistics(); }
};

//+------------------------------------------------------------------+
//| Constructor with symbol and magic number                        |
//+------------------------------------------------------------------+
StatisticsGroup::StatisticsGroup(string symbol = "", int magicNumber = 0)
    : OrderGroup(symbol)
{
    m_symbol = symbol == "" ? Symbol() : symbol;
    m_magicNumber = magicNumber;

    m_pool = NULL;

    ResetStatistics();

    // 如果提供了 magicNumber，創建對應的 TradingPool
    if (magicNumber > 0)
    {
        InitializePool();

    }
}



//+------------------------------------------------------------------+
//| Destructor                                                      |
//+------------------------------------------------------------------+
StatisticsGroup::~StatisticsGroup()
{
    // 總是刪除我們創建的對象（在構造函數中創建）

    if (m_pool != NULL)
    {
        delete m_pool;
    }
}

//+------------------------------------------------------------------+
//| Initialize TradingPool                                          |
//+------------------------------------------------------------------+
void StatisticsGroup::InitializePool()
{
    if (m_pool == NULL)
    {
        MatcherBuilder builder;
        OrderMatcher* matcher = builder.withSymbolMatcher(m_symbol)
                                       .withMagicNumberMatcher(m_magicNumber)
                                       .build();
        m_pool = new TradingPool(matcher);
    }
}









//+------------------------------------------------------------------+
//| 更新統計數據 - 重構版本（方案一）                               |
//|                                                                  |
//| 功能說明：                                                       |
//| - 使用方法拆分重構，將複雜邏輯分解為職責單一的小方法             |
//| - 充分利用 groupDoubleProperty 和基類方法                       |
//| - 提高代碼可讀性、可測試性和可維護性                             |
//+------------------------------------------------------------------+
void StatisticsGroup::UpdateStatistics()
{
    ResetStatistics();

    const int orderCount = size();
    if (orderCount == 0) return;

    m_stats.totalOrders = orderCount;

    // 分別計算不同類型的統計
    CalculateBasicStatistics();
    CalculateProfitStatistics();
    CalculateTimeStatistics();
    CalculateAverageValues();
}

//+------------------------------------------------------------------+
//| 計算基本統計 - 利用 groupDoubleProperty 和基類方法              |
//|                                                                  |
//| 功能說明：                                                       |
//| - 使用基類的 groupProfit() 和 groupLots() 方法                 |
//| - 利用 groupDoubleProperty 進行高效統計計算                     |
//| - 避免重複的訂單選擇和屬性訪問                                   |
//+------------------------------------------------------------------+
void StatisticsGroup::CalculateBasicStatistics()
{
    // 利用基類的高效方法計算總計
    m_stats.totalProfit = groupProfit();  // 使用 groupDoubleProperty(Order::Profit)
    m_stats.totalLots = groupLots();      // 使用 groupDoubleProperty(Order::Lots)
}

//+------------------------------------------------------------------+
//| 計算盈虧統計 - 找出最大盈利和最大虧損                           |
//|                                                                  |
//| 功能說明：                                                       |
//| - 遍歷所有訂單找出極值                                           |
//| - 只進行必要的訂單選擇和屬性訪問                                 |
//+------------------------------------------------------------------+
void StatisticsGroup::CalculateProfitStatistics()
{
    const int orderCount = size();

    for (int i = 0; i < orderCount; i++)
    {
        if (!Order::Select(get(i))) continue;

        const double profit = Order::Profit();

        // 更新極值
        if (profit > m_stats.maxProfit)
            m_stats.maxProfit = profit;
        if (profit < m_stats.maxLoss)
            m_stats.maxLoss = profit;
    }
}

//+------------------------------------------------------------------+
//| 計算時間統計 - 找出最早和最新訂單的時間和價格                   |
//|                                                                  |
//| 功能說明：                                                       |
//| - 找出最早和最新的訂單開倉時間                                   |
//| - 記錄對應的開倉價格                                             |
//| - 計算開倉訂單的平均持倉時間                                     |
//+------------------------------------------------------------------+
void StatisticsGroup::CalculateTimeStatistics()
{
    const int orderCount = size();
    const datetime currentTime = TimeCurrent();

    m_stats.oldestOrderTime = UINT_MAX;
    m_stats.newestOrderTime = 0;

    int holdingTimeSum = 0;
    int openOrderCount = 0;

    for (int i = 0; i < orderCount; i++)
    {
        if (!Order::Select(get(i))) continue;

        const datetime openTime = Order::OpenTime();
        const double openPrice = Order::OpenPrice();
        const datetime closeTime = Order::CloseTime();

        // 更新最早訂單信息
        if (openTime < m_stats.oldestOrderTime)
        {
            m_stats.oldestOrderTime = openTime;
            m_stats.oldestOrderPrice = openPrice;
        }

        // 更新最新訂單信息
        if (openTime > m_stats.newestOrderTime)
        {
            m_stats.newestOrderTime = openTime;
            m_stats.newestOrderPrice = openPrice;
        }

        // 計算持倉時間（僅開倉訂單）
        if (closeTime == 0)
        {
            holdingTimeSum += (int)(currentTime - openTime);
            openOrderCount++;
        }
    }

    // 計算平均持倉時間
    m_stats.avgHoldingTime = (openOrderCount > 0) ? holdingTimeSum / openOrderCount : 0;
}

//+------------------------------------------------------------------+
//| 計算平均值 - 利用基類的 groupAvg() 方法                         |
//|                                                                  |
//| 功能說明：                                                       |
//| - 使用基類的 groupAvg() 方法計算加權平均開倉價                  |
//| - 該方法已經實現了按手數加權的平均價格計算                       |
//| - 避免重複實現相同的邏輯                                         |
//+------------------------------------------------------------------+
void StatisticsGroup::CalculateAverageValues()
{
    // 利用基類的高效加權平均計算
    m_stats.avgOpenPrice = groupAvg();  // 按手數加權的平均開倉價
}

//+------------------------------------------------------------------+
//| Reset statistics                                                |
//+------------------------------------------------------------------+
void StatisticsGroup::ResetStatistics()
{
    m_stats.totalOrders = 0;
    m_stats.totalProfit = 0.0;
    m_stats.totalLots = 0.0;
    m_stats.avgOpenPrice = 0.0;
    m_stats.maxProfit = 0.0;
    m_stats.maxLoss = 0.0;
    m_stats.oldestOrderPrice = 0.0;
    m_stats.newestOrderPrice = 0.0;
    m_stats.oldestOrderTime = 0;
    m_stats.newestOrderTime = 0;
    m_stats.avgHoldingTime = 0;
}

//+------------------------------------------------------------------+
//| 執行訂單追蹤 - 子類可覆寫以自定義追蹤行為                        |
//+------------------------------------------------------------------+
void StatisticsGroup::ExecuteTracking() 
{
    foreachorder(m_pool)
    {
        add(Order::Ticket());
    }
}