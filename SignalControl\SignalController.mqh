#include "../Martingale/MartingaleRiskManager.mqh"
#include "../mql4-lib/History/Trigger.mqh"


enum ENUM_SIGNAL_TYPE
{
    SIGNAL_NONE = -1,
    SIGNAL_NEUTRAL = 0,
    SIGNAL_BUY = 1,
    SIGNAL_SELL = 2,
    SIGNAL_BOTH = 3
};

class SignalController
{
private:
    MartingaleRiskManager* m_riskManager;
    Trigger* m_trigger;

public:

    // Result
    ENUM_SIGNAL_TYPE    SignalType();
    bool         BuySignal();
    bool         SellSignal();
    bool         BothSignal();
    bool         NeutralSignal();
    bool         NoSignal();

    bool         ProcessSignal();
    bool         SystematicSignal();
    virtual bool PluginSignal();
};

bool SignalController::ProcessSignal()
{
    if(!SystematicSignal())
    {
        return false;
    }

    if(!PluginSignal())
    {
        return false;
    }

    return true;
}

bool SignalController::SystematicSignal()
{
    if(!TerminalInfoInteger(TERMINAL_CONNECTED))
    {
        return false;
    }
    
    if(!TerminalInfoInteger(TERMINAL_TRADE_ALLOWED))
    {
        return false;
    }

    return true;
}

bool SignalController::PluginSignal()
{
    m_riskManager
}

ENUM_SIGNAL_TYPE SignalController::SignalType()
{
    if(BuySignal())
    {
        return SIGNAL_BUY;
    }
    else if(SellSignal())
    {
        return SIGNAL_SELL;
    }
    else if(BothSignal())
    {
        return SIGNAL_BOTH;
    }
    else if(NeutralSignal())
    {
        return SIGNAL_NEUTRAL;
    }
    else
    {
        return SIGNAL_NONE;
    }
}

bool SignalController::BuySignal()
{
    if(!ProcessSignal())
    {
        return false;
    }

    if(m_trigger.isLong() && !m_trigger.isShort())
    {
        return true;
    }

    return false;
}

bool SignalController::SellSignal()
{
    if(!ProcessSignal())
    {
        return false;
    }

    if(m_trigger.isShort() && !m_trigger.isLong())
    {
        return true;
    }

    return false;
}

bool SignalController::BothSignal()
{
    if(!ProcessSignal())
    {
        return false;
    }

    if(m_trigger.isLong() && m_trigger.isShort())
    {
        return true;
    }

    return false;
}

bool SignalController::NeutralSignal()
{
    if(!ProcessSignal())
    {
        return false;
    }

    if(!m_trigger.isActivated())
    {
        return true;
    }

    return false;
}

bool SignalController::NoSignal()
{
    if(BuySignal() || SellSignal() || BothSignal() || NeutralSignal())
    {
        return false;
    }

    return true;
}


